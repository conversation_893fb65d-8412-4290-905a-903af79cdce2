'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ExternalLink, Github, ShoppingCart, MessageSquare, Brain } from 'lucide-react';
import { Button } from './ui/Button';

const Projects = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const projects = [
    {
      id: 1,
      title: 'Mega-Mall',
      description: 'A comprehensive full-stack ecommerce platform built with the MERN stack. Features include user authentication, product catalog, shopping cart, payment integration, order management, and admin dashboard.',
      longDescription: 'Mega-Mall is a feature-rich ecommerce solution that demonstrates advanced full-stack development skills. The platform includes real-time inventory management, secure payment processing, responsive design, and comprehensive admin tools for managing products, orders, and users.',
      icon: ShoppingCart,
      technologies: ['React.js', 'Node.js', 'Express.js', 'MongoDB', 'Stripe API', 'JWT', 'Tailwind CSS'],
      features: [
        'User Authentication & Authorization',
        'Product Catalog with Search & Filters',
        'Shopping Cart & Wishlist',
        'Secure Payment Integration',
        'Order Tracking & Management',
        'Admin Dashboard',
        'Responsive Design',
        'Real-time Notifications'
      ],
      github: '#',
      live: '#',
      image: '/api/placeholder/600/400',
    },
    {
      id: 2,
      title: 'True Feedback App',
      description: 'An innovative feedback collection platform that leverages AI to analyze and categorize user feedback. Built with Next.js and integrated with machine learning APIs for sentiment analysis and automated insights.',
      longDescription: 'True Feedback App revolutionizes how businesses collect and analyze customer feedback. Using advanced AI algorithms, it automatically categorizes feedback, performs sentiment analysis, and generates actionable insights to help businesses improve their services.',
      icon: MessageSquare,
      technologies: ['Next.js', 'TypeScript', 'Prisma', 'PostgreSQL', 'OpenAI API', 'Tailwind CSS', 'Vercel'],
      features: [
        'AI-Powered Sentiment Analysis',
        'Automated Feedback Categorization',
        'Real-time Analytics Dashboard',
        'Multi-channel Feedback Collection',
        'Customizable Feedback Forms',
        'Export & Reporting Tools',
        'Team Collaboration Features',
        'API Integration'
      ],
      github: '#',
      live: '#',
      image: '/api/placeholder/600/400',
    },
    {
      id: 3,
      title: 'AI Content Generator',
      description: 'A sophisticated content generation platform that uses multiple AI models to create blog posts, social media content, and marketing copy. Features include template management, content optimization, and team collaboration.',
      longDescription: 'This AI-powered content generation platform helps businesses and content creators produce high-quality content efficiently. It integrates multiple AI models and provides advanced features for content optimization, SEO analysis, and team workflow management.',
      icon: Brain,
      technologies: ['React.js', 'Node.js', 'Express.js', 'MongoDB', 'OpenAI API', 'Claude API', 'Redis', 'AWS S3'],
      features: [
        'Multi-Model AI Integration',
        'Content Template Library',
        'SEO Optimization Tools',
        'Plagiarism Detection',
        'Team Collaboration',
        'Content Scheduling',
        'Analytics & Performance Tracking',
        'Custom Brand Voice Training'
      ],
      github: '#',
      live: '#',
      image: '/api/placeholder/600/400',
    },
  ];

  return (
    <section id="projects" className="py-20 bg-secondary/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="max-w-7xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
              Featured <span className="gradient-text">Projects</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Showcasing innovative solutions built with modern technologies and AI integrations
            </p>
          </motion.div>

          {/* Projects Grid */}
          <div className="space-y-16">
            {projects.map((project, index) => {
              const IconComponent = project.icon;
              const isEven = index % 2 === 0;
              
              return (
                <motion.div
                  key={project.id}
                  variants={itemVariants}
                  className={`grid lg:grid-cols-2 gap-8 items-center ${
                    !isEven ? 'lg:grid-flow-col-dense' : ''
                  }`}
                >
                  {/* Project Image */}
                  <div className={`${!isEven ? 'lg:col-start-2' : ''}`}>
                    <div className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-lg blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                      <div className="relative bg-card border border-border rounded-lg p-8 hover:shadow-xl transition-shadow duration-300">
                        <div className="flex items-center justify-center h-64 bg-muted rounded-lg">
                          <IconComponent className="w-24 h-24 text-primary" />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className={`space-y-6 ${!isEven ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                    <div>
                      <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
                        {project.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed mb-6">
                        {project.description}
                      </p>
                    </div>

                    {/* Technologies */}
                    <div>
                      <h4 className="text-sm font-semibold text-foreground mb-3 uppercase tracking-wide">
                        Technologies Used
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech) => (
                          <span
                            key={tech}
                            className="px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Key Features */}
                    <div>
                      <h4 className="text-sm font-semibold text-foreground mb-3 uppercase tracking-wide">
                        Key Features
                      </h4>
                      <ul className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-muted-foreground">
                        {project.features.slice(0, 6).map((feature) => (
                          <li key={feature} className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-primary rounded-full mr-2 flex-shrink-0"></span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button
                        variant="primary"
                        size="md"
                        className="flex items-center justify-center"
                        onClick={() => window.open(project.live, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Live Demo
                      </Button>
                      <Button
                        variant="outline"
                        size="md"
                        className="flex items-center justify-center"
                        onClick={() => window.open(project.github, '_blank')}
                      >
                        <Github className="w-4 h-4 mr-2" />
                        View Code
                      </Button>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
