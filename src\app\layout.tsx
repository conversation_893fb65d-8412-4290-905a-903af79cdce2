import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Sameer Dev - Full Stack Developer | MERN Stack & Next.js Expert",
  description: "Professional portfolio of <PERSON><PERSON>, a skilled Full Stack Developer specializing in MERN Stack, Next.js, and AI integrations. Showcasing innovative web applications including Mega-Mall ecommerce platform and True Feedback App.",
  keywords: "Full Stack Developer, MERN Stack, Next.js, React, Node.js, MongoDB, Express.js, AI Integration, Web Development, JavaScript, TypeScript",
  authors: [{ name: "<PERSON><PERSON>" }],
  creator: "<PERSON><PERSON> Dev",
  openGraph: {
    title: "<PERSON><PERSON> Dev - Full Stack Developer Portfolio",
    description: "Explore the portfolio of Sameer Dev, featuring cutting-edge web applications built with MERN Stack, Next.js, and AI technologies.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sameer Dev - Full Stack Developer Portfolio",
    description: "Professional portfolio showcasing MERN Stack and Next.js expertise with AI integrations.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
