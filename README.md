# Sameer Dev - Portfolio Website

A modern, responsive portfolio website showcasing full-stack development expertise with MERN stack, Next.js, and AI integrations.

## 🚀 Features

- **Modern Design**: Clean, professional layout with smooth animations
- **Responsive**: Optimized for all device sizes (mobile, tablet, desktop)
- **Performance**: Fast loading with Next.js optimization
- **SEO Optimized**: Meta tags, sitemap, and robots.txt included
- **Interactive**: Smooth scrolling navigation and hover effects
- **AI Integration Showcase**: Highlighting AI-powered project capabilities
- **Contact Form**: Functional contact form with validation
- **Dark/Light Mode**: Automatic theme switching based on system preference

## 🛠️ Tech Stack

- **Framework**: Next.js 15.4.2
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.0
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Fonts**: Geist Sans & Geist Mono
- **Deployment**: Vercel (recommended)

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/sameerdev/portfolio.git
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to see the portfolio.

## 🎨 Customization

### Personal Information
Update the following files with your personal information:

1. **src/app/layout.tsx** - Update metadata (title, description, keywords)
2. **src/components/Hero.tsx** - Update name, role, description, and social links
3. **src/components/About.tsx** - Update about section content and stats
4. **src/components/Contact.tsx** - Update contact information
5. **src/components/Footer.tsx** - Update footer information

### Projects
Edit **src/components/Projects.tsx** to showcase your projects:
- Update project titles, descriptions, and technologies
- Add your GitHub and live demo links
- Replace placeholder images with actual project screenshots

### Skills
Modify **src/components/Skills.tsx** to reflect your skill levels:
- Update skill categories and individual skills
- Adjust skill level percentages
- Add or remove technologies as needed

### Styling
The design system is defined in **src/app/globals.css**:
- Color scheme variables
- Custom animations
- Utility classes

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy with one click

### Other Platforms
The portfolio can be deployed on any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- Render

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

## 🎯 Performance Features

- **Image Optimization**: Next.js automatic image optimization
- **Code Splitting**: Automatic code splitting for faster loads
- **SEO**: Comprehensive meta tags and structured data
- **Accessibility**: ARIA labels and keyboard navigation support
- **Core Web Vitals**: Optimized for Google's Core Web Vitals

## 📄 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and design system
│   ├── layout.tsx           # Root layout with metadata
│   ├── page.tsx             # Main page component
│   ├── loading.tsx          # Loading component
│   ├── sitemap.ts           # SEO sitemap
│   └── robots.ts            # Robots.txt configuration
├── components/
│   ├── ui/
│   │   ├── Button.tsx       # Reusable button component
│   │   └── Container.tsx    # Container wrapper component
│   ├── Header.tsx           # Navigation header
│   ├── Hero.tsx             # Hero section
│   ├── About.tsx            # About section
│   ├── Skills.tsx           # Skills showcase
│   ├── Projects.tsx         # Projects portfolio
│   ├── Contact.tsx          # Contact form
│   └── Footer.tsx           # Footer component
└── lib/
    └── utils.ts             # Utility functions
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 📞 Support

If you have any questions or need help customizing the portfolio, feel free to reach out:

- Email: <EMAIL>
- LinkedIn: [Sameer Dev](https://linkedin.com/in/sameerdev)
- GitHub: [sameerdev](https://github.com/sameerdev)

---

**Built with ❤️ using Next.js and Tailwind CSS**
